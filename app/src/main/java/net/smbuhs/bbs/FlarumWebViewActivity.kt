package net.smbuhs.bbs

import android.annotation.TargetApi
import android.app.Activity
import android.content.ClipData
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.util.Log
import android.webkit.*
import android.widget.Toast
import androidx.activity.ComponentActivity
import androidx.activity.result.contract.ActivityResultContracts

class FlarumWebViewActivity : ComponentActivity() {
    
    private lateinit var webView: WebView
    private var uploadMessage: ValueCallback<Array<Uri>>? = null
    
    companion object {
        private const val TAG = "FlarumWebView"
        private const val FLARUM_URL = "https://bbs.smbuhs.net"
    }
    
    // 使用新的 Activity Result API
    private val fileChooserLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        handleFileChooserResult(result.resultCode, result.data)
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        setupWebView()
        loadFlarumSite()
    }
    
    private fun setupWebView() {
        webView = WebView(this)
        setContentView(webView)
        
        // 基本 WebView 设置
        webView.settings.apply {
            javaScriptEnabled = true
            domStorageEnabled = true
            allowFileAccess = true
            allowContentAccess = true
            allowFileAccessFromFileURLs = true
            allowUniversalAccessFromFileURLs = true
            mixedContentMode = WebSettings.MIXED_CONTENT_ALWAYS_ALLOW
            cacheMode = WebSettings.LOAD_DEFAULT
            
            // 启用缩放
            setSupportZoom(true)
            builtInZoomControls = true
            displayZoomControls = false
            
            // 设置用户代理
            userAgentString = userAgentString + " FlarumApp/1.0"
        }
        
        // 设置 WebViewClient
        webView.webViewClient = object : WebViewClient() {
            override fun shouldOverrideUrlLoading(view: WebView?, request: WebResourceRequest?): Boolean {
                val url = request?.url?.toString()
                return if (url != null && url.startsWith("http")) {
                    view?.loadUrl(url)
                    true
                } else {
                    false
                }
            }
            
            override fun onPageFinished(view: WebView?, url: String?) {
                super.onPageFinished(view, url)
                Log.d(TAG, "Page loaded: $url")
            }
            
            override fun onReceivedError(view: WebView?, request: WebResourceRequest?, error: WebResourceError?) {
                super.onReceivedError(view, request, error)
                Log.e(TAG, "WebView error: ${error?.description}")
                Toast.makeText(this@FlarumWebViewActivity, "加载失败，请检查网络连接", Toast.LENGTH_SHORT).show()
            }
        }
        
        // 设置 WebChromeClient 处理文件上传
        webView.webChromeClient = object : WebChromeClient() {
            override fun onShowFileChooser(
                webView: WebView?,
                filePathCallback: ValueCallback<Array<Uri>>?,
                fileChooserParams: FileChooserParams?
            ): Boolean {
                uploadMessage = filePathCallback
                openFileChooser(fileChooserParams)
                return true
            }
            
            override fun onProgressChanged(view: WebView?, newProgress: Int) {
                super.onProgressChanged(view, newProgress)
                // 可以在这里添加进度条显示
            }
        }
    }
    
    private fun loadFlarumSite() {
        webView.loadUrl(FLARUM_URL)
    }
    
    private fun openFileChooser(fileChooserParams: WebChromeClient.FileChooserParams?) {
        try {
            val intent = Intent(Intent.ACTION_GET_CONTENT).apply {
                addCategory(Intent.CATEGORY_OPENABLE)
                type = "*/*"
                
                // 支持多文件选择
                putExtra(Intent.EXTRA_ALLOW_MULTIPLE, true)
                
                // 处理 accept 属性
                fileChooserParams?.acceptTypes?.let { acceptTypes ->
                    if (acceptTypes.isNotEmpty() && Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
                        putExtra(Intent.EXTRA_MIME_TYPES, acceptTypes)
                    }
                }
            }
            
            val chooserIntent = Intent.createChooser(intent, "选择文件")
            fileChooserLauncher.launch(chooserIntent)
            
        } catch (e: Exception) {
            Log.e(TAG, "Error opening file chooser", e)
            uploadMessage?.onReceiveValue(null)
            uploadMessage = null
            Toast.makeText(this, "无法打开文件选择器", Toast.LENGTH_SHORT).show()
        }
    }
    
    private fun handleFileChooserResult(resultCode: Int, data: Intent?) {
        val results = if (resultCode == Activity.RESULT_OK && data != null) {
            getSelectedFiles(data)
        } else {
            null
        }
        
        uploadMessage?.onReceiveValue(results)
        uploadMessage = null
    }
    
    @TargetApi(Build.VERSION_CODES.LOLLIPOP)
    private fun getSelectedFiles(data: Intent): Array<Uri>? {
        return try {
            val clipData = data.clipData
            if (clipData != null) {
                // 多文件选择
                Array(clipData.itemCount) { i ->
                    clipData.getItemAt(i).uri
                }
            } else {
                // 单文件选择
                data.data?.let { arrayOf(it) }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error processing selected files", e)
            null
        }
    }
    
    override fun onBackPressed() {
        if (webView.canGoBack()) {
            webView.goBack()
        } else {
            super.onBackPressed()
        }
    }
    
    override fun onDestroy() {
        super.onDestroy()
        webView.destroy()
    }
}
